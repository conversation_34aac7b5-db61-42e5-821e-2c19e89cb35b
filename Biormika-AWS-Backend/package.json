{"name": "biormika-aws-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "./scripts/run-tests.sh", "test:unit": "pytest tests/unit -v", "test:integration": "pytest tests/integration -v", "test:coverage": "pytest --cov=. --cov-report=html tests/", "offline": "serverless offline start --stage dev --aws-profile kirtan-default --verbose", "build:image": "./scripts/build-and-push.sh dev us-east-1 kirtan-default", "push:ecr": "./scripts/build-and-push.sh dev us-east-1 kirtan-default", "deploy:dev": "npm run build:image && serverless deploy --stage dev --aws-profile kirtan-default", "deploy:dev:local": "serverless deploy --stage dev --aws-profile kirtan-default", "deploy:prod": "./scripts/build-and-push.sh prod us-east-1 kirtan-default && serverless deploy --stage prod --aws-profile kirtan-default"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"serverless": "^3.40.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-offline": "^13.9.0", "serverless-python-requirements": "^6.1.2"}}