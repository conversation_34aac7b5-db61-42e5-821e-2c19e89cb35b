import { apiClient } from '../lib/axios'
import { API_ENDPOINTS } from '../constants/api'
import type { ApiResponse } from '../types/api'

export interface MultipartUploadConfig {
  fileId: string
  file: File
  chunkSize?: number
  onProgress?: (progress: MultipartUploadProgress) => void
  onChunkComplete?: (chunkNumber: number, totalChunks: number) => void
  onError?: (error: Error, chunkNumber?: number) => void
}

export interface MultipartUploadProgress {
  fileId: string
  totalProgress: number
  uploadedBytes: number
  totalBytes: number
  chunksCompleted: number
  totalChunks: number
  currentChunkProgress: Record<number, number>
}

export interface MultipartInitResponse {
  fileId: string
  uploadId: string
  s3Key: string
  bucket: string
}

export interface PartUploadUrl {
  partNumber: number
  url: string
}

export interface CompletedPart {
  partNumber: number
  eTag: string
}

interface MultipartUploadUrlsResponse {
  urls: PartUploadUrl[]
}

const DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024 // 10MB
const MIN_CHUNK_SIZE = 5 * 1024 * 1024 // 5MB (AWS S3 minimum)
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY_BASE = 1000 // 1 second

export class MultipartUploadService {
  private abortController: AbortController | null = null

  async uploadFile(config: MultipartUploadConfig): Promise<void> {
    const {
      fileId,
      file,
      chunkSize = DEFAULT_CHUNK_SIZE,
      onProgress,
      onChunkComplete,
      onError,
    } = config

    this.abortController = new AbortController()

    try {
      // Validate chunk size
      const validatedChunkSize = this.validateChunkSize(chunkSize, file.size)

      // Calculate chunks
      const totalChunks = Math.ceil(file.size / validatedChunkSize)
      const chunks = this.createChunks(file, validatedChunkSize)

      console.log(
        `Starting multipart upload: ${file.name}, size: ${file.size}, chunks: ${totalChunks}`
      )

      // Initialize multipart upload
      const initResponse = await this.initializeMultipartUpload(file)

      // Get presigned URLs for all parts
      const uploadUrls = await this.getUploadUrls(
        initResponse.uploadId,
        initResponse.s3Key,
        totalChunks
      )

      // Track progress
      const progress: MultipartUploadProgress = {
        fileId,
        totalProgress: 0,
        uploadedBytes: 0,
        totalBytes: file.size,
        chunksCompleted: 0,
        totalChunks,
        currentChunkProgress: {},
      }

      // Upload chunks sequentially for simplicity and better error handling
      const completedParts = await this.uploadChunksSequentially(
        chunks,
        uploadUrls,
        (chunkNumber, chunkProgress) => {
          progress.currentChunkProgress[chunkNumber] = chunkProgress

          // Calculate total progress
          const baseProgress = progress.chunksCompleted * validatedChunkSize
          const currentChunksProgress = Object.values(progress.currentChunkProgress).reduce(
            (sum, p) => sum + p,
            0
          )

          progress.uploadedBytes = Math.min(baseProgress + currentChunksProgress, file.size)
          progress.totalProgress = Math.round((progress.uploadedBytes / file.size) * 100)

          onProgress?.(progress)
        },
        (chunkNumber) => {
          progress.chunksCompleted++
          delete progress.currentChunkProgress[chunkNumber]
          onChunkComplete?.(chunkNumber, totalChunks)
        },
        onError
      )

      // Complete multipart upload
      await this.completeMultipartUpload(
        fileId,
        initResponse.uploadId,
        initResponse.s3Key,
        completedParts
      )

      console.log(`Multipart upload completed successfully: ${file.name}`)

      // Final progress update
      if (onProgress) {
        onProgress({
          ...progress,
          totalProgress: 100,
          uploadedBytes: file.size,
          chunksCompleted: totalChunks,
        })
      }
    } catch (error) {
      console.error('Multipart upload failed:', error)
      if (this.abortController?.signal.aborted) {
        throw new Error('Upload cancelled')
      }
      throw error
    } finally {
      this.abortController = null
    }
  }

  abort(): void {
    this.abortController?.abort()
  }

  private validateChunkSize(chunkSize: number, fileSize: number): number {
    // For files smaller than minimum chunk size, use the file size
    if (fileSize <= MIN_CHUNK_SIZE) {
      console.log(`File size (${fileSize}) is smaller than minimum chunk size, using file size`)
      return fileSize
    }

    // Ensure chunk size is at least the minimum (except for last part)
    const validatedSize = Math.max(chunkSize, MIN_CHUNK_SIZE)

    // Ensure we don't exceed the maximum number of parts (10,000)
    const maxChunkSize = Math.ceil(fileSize / 10000)
    const finalChunkSize = Math.max(validatedSize, maxChunkSize)

    // Calculate how many parts this will create
    const totalParts = Math.ceil(fileSize / finalChunkSize)

    console.log(
      `Chunk size validation: requested=${chunkSize}, validated=${finalChunkSize}, total parts=${totalParts}`
    )

    if (totalParts > 10000) {
      throw new Error(`File too large: would require ${totalParts} parts (maximum is 10,000)`)
    }

    return finalChunkSize
  }

  private createChunks(file: File, chunkSize: number): Blob[] {
    const chunks: Blob[] = []
    let offset = 0

    while (offset < file.size) {
      const end = Math.min(offset + chunkSize, file.size)
      chunks.push(file.slice(offset, end))
      offset = end
    }

    return chunks
  }

  private async initializeMultipartUpload(file: File): Promise<MultipartInitResponse> {
    const response = await apiClient.post<ApiResponse<MultipartInitResponse>>(
      API_ENDPOINTS.files.multipart.init,
      {
        fileName: file.name,
        fileSize: file.size,
        contentType: file.type || 'application/octet-stream',
      }
    )

    return response.data.data
  }

  private async getUploadUrls(
    uploadId: string,
    s3Key: string,
    parts: number
  ): Promise<PartUploadUrl[]> {
    const response = await apiClient.post<ApiResponse<MultipartUploadUrlsResponse>>(
      API_ENDPOINTS.files.multipart.urls,
      {
        uploadId,
        s3Key,
        parts,
      }
    )

    return response.data.data.urls
  }

  private async uploadChunksSequentially(
    chunks: Blob[],
    uploadUrls: PartUploadUrl[],
    onChunkProgress: (chunkNumber: number, progress: number) => void,
    onChunkComplete: (chunkNumber: number) => void,
    onError?: (error: Error, chunkNumber?: number) => void
  ): Promise<CompletedPart[]> {
    const completedParts: CompletedPart[] = []

    for (let i = 0; i < uploadUrls.length; i++) {
      const partUpload = uploadUrls[i]
      const chunk = chunks[i]

      console.log(`Uploading part ${partUpload.partNumber}/${uploadUrls.length}`)

      try {
        const completedPart = await this.uploadChunkWithRetry(
          chunk,
          partUpload,
          (progress) => onChunkProgress(partUpload.partNumber, progress),
          onError
        )

        completedParts.push(completedPart)
        onChunkComplete(partUpload.partNumber)

        console.log(
          `Part ${partUpload.partNumber} uploaded successfully, ETag: ${completedPart.eTag}`
        )
      } catch (error) {
        console.error(`Failed to upload part ${partUpload.partNumber}:`, error)
        throw error
      }
    }

    return completedParts.sort((a, b) => a.partNumber - b.partNumber)
  }

  private async uploadWithProgress(
    url: string,
    chunk: Blob,
    onProgress: (progress: number) => void
  ): Promise<Response> {
    console.log(`Starting upload to S3, chunk size: ${chunk.size} bytes`)

    const response = await fetch(url, {
      method: 'PUT',
      body: chunk,
      signal: this.abortController?.signal,
      // Explicitly don't set any headers - S3 presigned URLs for multipart
      // uploads are generated WITHOUT Content-Type in the signature
      // Adding any Content-Type header will cause SignatureDoesNotMatch error
    })

    console.log(`S3 upload response: ${response.status} ${response.statusText}`)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    // Report progress as complete once the request finishes
    onProgress(chunk.size)

    return response
  }

  private async uploadChunkWithRetry(
    chunk: Blob,
    partUpload: PartUploadUrl,
    onProgress: (progress: number) => void,
    onError?: (error: Error, chunkNumber?: number) => void,
    attempt = 1
  ): Promise<CompletedPart> {
    try {
      console.log(`Uploading part ${partUpload.partNumber}, attempt ${attempt}`)

      const response = await this.uploadWithProgress(partUpload.url, chunk, onProgress)

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`S3 upload failed: ${response.status} ${response.statusText}. ${errorText}`)
      }

      // Try different case variations for ETag header
      const eTag =
        response.headers.get('etag') || response.headers.get('ETag') || response.headers.get('ETAG')

      console.log(`Part ${partUpload.partNumber} response ETag:`, eTag)

      if (!eTag) {
        console.error(`No ETag received for part ${partUpload.partNumber}`)
        console.error('Available headers:', Object.fromEntries(response.headers.entries()))
        throw new Error(`No ETag received for part ${partUpload.partNumber}`)
      }

      const cleanETag = eTag.replace(/"/g, '') // Remove quotes from ETag
      console.log(`Part ${partUpload.partNumber} clean ETag:`, cleanETag)

      return {
        partNumber: partUpload.partNumber,
        eTag: cleanETag,
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw error
      }

      console.error(`Part ${partUpload.partNumber} upload failed (attempt ${attempt}):`, error)

      if (attempt < MAX_RETRY_ATTEMPTS) {
        const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1)
        console.log(
          `Retrying part ${partUpload.partNumber} upload in ${delay}ms (attempt ${
            attempt + 1
          }/${MAX_RETRY_ATTEMPTS})`
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
        return this.uploadChunkWithRetry(chunk, partUpload, onProgress, onError, attempt + 1)
      }

      const finalErrorMessage = `Failed to upload part ${
        partUpload.partNumber
      } after ${MAX_RETRY_ATTEMPTS} attempts: ${
        error instanceof Error ? error.message : String(error)
      }`
      const uploadError = new Error(finalErrorMessage)
      onError?.(uploadError, partUpload.partNumber)
      throw uploadError
    }
  }

  private async completeMultipartUpload(
    fileId: string,
    uploadId: string,
    s3Key: string,
    parts: CompletedPart[]
  ): Promise<void> {
    await apiClient.post(API_ENDPOINTS.files.multipart.complete, {
      fileId,
      uploadId,
      s3Key,
      parts: parts.map((part) => ({
        partNumber: part.partNumber,
        eTag: part.eTag,
      })),
    })
  }

  async abortMultipartUpload(fileId: string, uploadId: string, s3Key: string): Promise<void> {
    try {
      await apiClient.post(API_ENDPOINTS.files.multipart.abort, {
        fileId,
        uploadId,
        s3Key,
      })
    } catch (error) {
      console.error('Failed to abort multipart upload:', error)
    }
  }
}
